namespace HotPreview.Tooling.Services;

/// <summary>
/// Service interface for reporting application status messages.
/// </summary>
public interface IStatusReporter
{
    /// <summary>
    /// Event raised when the status message changes.
    /// </summary>
    event EventHandler<string>? StatusChanged;

    /// <summary>
    /// Updates the current status message.
    /// </summary>
    /// <param name="message">The status message to display.</param>
    void UpdateStatus(string message);

    /// <summary>
    /// Clears the status message (sets to default).
    /// </summary>
    void ClearStatus();
}
