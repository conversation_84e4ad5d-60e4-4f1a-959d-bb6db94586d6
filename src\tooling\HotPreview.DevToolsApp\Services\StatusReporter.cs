using HotPreview.Tooling.Services;

namespace HotPreview.DevToolsApp.Services;

/// <summary>
/// Implementation of IStatusReporter for managing application status messages.
/// </summary>
public class StatusReporter : IStatusReporter
{
    public event EventHandler<string>? StatusChanged;

    public void UpdateStatus(string message)
    {
        StatusChanged?.Invoke(this, message);
    }

    public void ClearStatus()
    {
        UpdateStatus("Ready");
    }
}
