namespace HotPreview.DevToolsApp.Services;

/// <summary>
/// Example service showing how to use IStatusService during long-running operations.
/// </summary>
public class ExampleLongRunningService
{
    private readonly IStatusService _statusService;
    
    public ExampleLongRunningService(IStatusService statusService)
    {
        _statusService = statusService;
    }
    
    public async Task ProcessProjectAsync(string projectPath)
    {
        try
        {
            _statusService.UpdateStatus("Loading project...");
            await Task.Delay(1000); // Simulate work
            
            _statusService.UpdateStatus("Analyzing components...");
            await Task.Delay(2000); // Simulate work
            
            _statusService.UpdateStatus("Building component tree...");
            await Task.Delay(1500); // Simulate work
            
            _statusService.UpdateStatus("Generating previews...");
            await Task.Delay(3000); // Simulate work
            
            _statusService.UpdateStatus("Project loaded successfully");
            
            // Clear status after a delay
            await Task.Delay(2000);
            _statusService.ClearStatus();
        }
        catch (Exception ex)
        {
            _statusService.UpdateStatus($"Error: {ex.Message}");
        }
    }
    
    public async Task UpdateSnapshotsAsync(IEnumerable<string> components)
    {
        try
        {
            var componentList = components.ToList();
            
            for (int i = 0; i < componentList.Count; i++)
            {
                _statusService.UpdateStatus($"Updating snapshot {i + 1} of {componentList.Count}: {componentList[i]}");
                await Task.Delay(500); // Simulate snapshot generation
            }
            
            _statusService.UpdateStatus("All snapshots updated successfully");
            await Task.Delay(2000);
            _statusService.ClearStatus();
        }
        catch (Exception ex)
        {
            _statusService.UpdateStatus($"Snapshot update failed: {ex.Message}");
        }
    }
}
